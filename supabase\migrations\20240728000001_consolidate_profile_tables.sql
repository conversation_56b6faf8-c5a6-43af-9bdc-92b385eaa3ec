-- Consolidate Profile Tables and Fix Schema Issues
-- This migration consolidates the duplicate profile tables and ensures proper data flow

-- Step 1: Create a unified user_profiles table with email field
DROP TABLE IF EXISTS public.user_profiles_new CASCADE;
CREATE TABLE public.user_profiles_new (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
  email TEXT, -- Add email field for easier admin management
  full_name TEXT,
  is_subscribed BOOLEAN DEFAULT false,
  is_admin BOOLEAN DEFAULT false,
  subscription_expires_at TIMESTAMPTZ,
  subscription_status TEXT DEFAULT 'free',
  subscription_plan TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Step 2: Migrate data from both existing tables
-- First, migrate from profiles table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'profiles') THEN
    INSERT INTO public.user_profiles_new (user_id, email, full_name, is_subscribed, is_admin, subscription_expires_at, subscription_status, subscription_plan, created_at, updated_at)
    SELECT
      p.id as user_id,
      COALESCE(p.email, au.email) as email,
      COALESCE(p.full_name, au.raw_user_meta_data->>'full_name') as full_name,
      COALESCE(p.is_subscribed, false) as is_subscribed,
      COALESCE(p.is_admin, false) as is_admin,
      COALESCE(p.subscription_ends_at, p.subscription_expires_at) as subscription_expires_at,
      COALESCE(p.subscription_status, 'free') as subscription_status,
      p.subscription_plan,
      COALESCE(p.created_at, au.created_at) as created_at,
      COALESCE(p.updated_at, au.updated_at) as updated_at
    FROM public.profiles p
    LEFT JOIN auth.users au ON p.id = au.id
    ON CONFLICT (user_id) DO NOTHING;
  END IF;
END $$;

-- Then, migrate from user_profiles table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_profiles') THEN
    INSERT INTO public.user_profiles_new (user_id, email, is_subscribed, is_admin, subscription_expires_at, created_at, updated_at)
    SELECT
      up.user_id,
      COALESCE(au.email) as email,
      COALESCE(up.is_subscribed, false) as is_subscribed,
      COALESCE(up.is_admin, false) as is_admin,
      up.subscription_expires_at,
      COALESCE(up.created_at, au.created_at) as created_at,
      COALESCE(up.updated_at, au.updated_at) as updated_at
    FROM public.user_profiles up
    LEFT JOIN auth.users au ON up.user_id = au.id
    ON CONFLICT (user_id) DO UPDATE SET
      is_subscribed = COALESCE(EXCLUDED.is_subscribed, user_profiles_new.is_subscribed),
      is_admin = COALESCE(EXCLUDED.is_admin, user_profiles_new.is_admin),
      subscription_expires_at = COALESCE(EXCLUDED.subscription_expires_at, user_profiles_new.subscription_expires_at),
      updated_at = now();
  END IF;
END $$;

-- Finally, ensure all auth.users have profiles
INSERT INTO public.user_profiles_new (user_id, email, full_name, is_subscribed, is_admin, created_at, updated_at)
SELECT
  au.id as user_id,
  au.email,
  au.raw_user_meta_data->>'full_name' as full_name,
  false as is_subscribed,
  false as is_admin,
  au.created_at,
  au.updated_at
FROM auth.users au
WHERE au.id NOT IN (SELECT user_id FROM public.user_profiles_new)
ON CONFLICT (user_id) DO NOTHING;

-- Step 3: Drop old tables and rename new one
DROP TABLE IF EXISTS public.profiles CASCADE;
DROP TABLE IF EXISTS public.user_profiles CASCADE;
ALTER TABLE public.user_profiles_new RENAME TO user_profiles;

-- Step 4: Enable RLS and create policies
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Users can view their own profile
DROP POLICY IF EXISTS "Users can view their own profile" ON public.user_profiles;
CREATE POLICY "Users can view their own profile"
  ON public.user_profiles FOR SELECT
  USING (auth.uid() = user_id);

-- Users can update their own profile
DROP POLICY IF EXISTS "Users can update their own profile" ON public.user_profiles;
CREATE POLICY "Users can update their own profile"
  ON public.user_profiles FOR UPDATE
  USING (auth.uid() = user_id);

-- Admins can view and manage all profiles
DROP POLICY IF EXISTS "Admins can manage all profiles" ON public.user_profiles;
CREATE POLICY "Admins can manage all profiles"
  ON public.user_profiles FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM public.user_profiles up
      WHERE up.user_id = auth.uid() AND up.is_admin = true
    )
  );

-- Step 5: Create updated triggers
DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON public.user_profiles;
CREATE TRIGGER update_user_profiles_updated_at
  BEFORE UPDATE ON public.user_profiles
  FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Create function to sync email from auth.users
CREATE OR REPLACE FUNCTION public.sync_user_email()
RETURNS TRIGGER AS $$
BEGIN
  -- Update email in user_profiles when auth.users email changes
  UPDATE public.user_profiles 
  SET email = NEW.email, updated_at = now()
  WHERE user_id = NEW.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to sync email changes
DROP TRIGGER IF EXISTS sync_auth_user_email ON auth.users;
CREATE TRIGGER sync_auth_user_email
  AFTER UPDATE OF email ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.sync_user_email();

-- Step 6: Update the new user creation trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert into user_profiles with email
  INSERT INTO public.user_profiles (user_id, email, full_name, is_subscribed, is_admin)
  VALUES (
    NEW.id, 
    NEW.email,
    NEW.raw_user_meta_data->>'full_name',
    false, 
    false
  )
  ON CONFLICT (user_id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = COALESCE(EXCLUDED.full_name, user_profiles.full_name),
    updated_at = now();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Step 7: Create function to get user profile with email
CREATE OR REPLACE FUNCTION public.get_user_profile_with_email(user_uuid UUID)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  email TEXT,
  full_name TEXT,
  is_subscribed BOOLEAN,
  is_admin BOOLEAN,
  subscription_expires_at TIMESTAMPTZ,
  subscription_status TEXT,
  subscription_plan TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    up.id,
    up.user_id,
    COALESCE(up.email, au.email) as email,
    up.full_name,
    up.is_subscribed,
    up.is_admin,
    up.subscription_expires_at,
    up.subscription_status,
    up.subscription_plan,
    up.created_at,
    up.updated_at
  FROM public.user_profiles up
  LEFT JOIN auth.users au ON up.user_id = au.id
  WHERE up.user_id = user_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 8: Create function to get all user profiles for admin
CREATE OR REPLACE FUNCTION public.get_all_user_profiles()
RETURNS TABLE (
  id UUID,
  user_id UUID,
  email TEXT,
  full_name TEXT,
  is_subscribed BOOLEAN,
  is_admin BOOLEAN,
  subscription_expires_at TIMESTAMPTZ,
  subscription_status TEXT,
  subscription_plan TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ,
  last_sign_in_at TIMESTAMPTZ
) AS $$
BEGIN
  -- Check if current user is admin
  IF NOT EXISTS (
    SELECT 1 FROM public.user_profiles 
    WHERE user_id = auth.uid() AND is_admin = true
  ) THEN
    RAISE EXCEPTION 'Access denied. Admin privileges required.';
  END IF;

  RETURN QUERY
  SELECT 
    up.id,
    up.user_id,
    COALESCE(up.email, au.email) as email,
    up.full_name,
    up.is_subscribed,
    up.is_admin,
    up.subscription_expires_at,
    up.subscription_status,
    up.subscription_plan,
    up.created_at,
    up.updated_at,
    au.last_sign_in_at
  FROM public.user_profiles up
  LEFT JOIN auth.users au ON up.user_id = au.id
  ORDER BY up.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 9: Update payment success function to use consolidated table
CREATE OR REPLACE FUNCTION public.handle_payment_success(
  p_user_email TEXT,
  p_plan_id TEXT,
  p_amount DECIMAL,
  p_reference TEXT
)
RETURNS JSONB AS $$
DECLARE
  user_record RECORD;
  subscription_id UUID;
  payment_id UUID;
  start_date TIMESTAMPTZ;
  end_date TIMESTAMPTZ;
  result JSONB;
BEGIN
  -- Get user by email
  SELECT id INTO user_record FROM auth.users WHERE email = p_user_email;
  
  IF user_record IS NULL THEN
    RETURN jsonb_build_object('success', false, 'error', 'User not found');
  END IF;
  
  -- Calculate subscription dates
  start_date := NOW();
  CASE p_plan_id
    WHEN 'basic' THEN end_date := start_date + INTERVAL '1 month';
    WHEN 'pro' THEN end_date := start_date + INTERVAL '3 months';
    WHEN 'elite' THEN end_date := start_date + INTERVAL '1 year';
    ELSE end_date := start_date + INTERVAL '1 month';
  END CASE;
  
  -- Record payment
  INSERT INTO public.payments (
    user_id, amount, status, provider_payment_id, metadata
  ) VALUES (
    user_record.id, p_amount, 'completed', p_reference,
    jsonb_build_object('plan_id', p_plan_id, 'reference', p_reference)
  ) RETURNING id INTO payment_id;
  
  -- Create or update subscription
  INSERT INTO public.subscriptions (
    user_id, plan_id, amount_paid, start_date, end_date, 
    is_active, last_payment_reference
  ) VALUES (
    user_record.id, p_plan_id, p_amount, start_date, end_date,
    true, p_reference
  ) ON CONFLICT (user_id) DO UPDATE SET
    plan_id = EXCLUDED.plan_id,
    amount_paid = EXCLUDED.amount_paid,
    start_date = EXCLUDED.start_date,
    end_date = EXCLUDED.end_date,
    is_active = true,
    last_payment_reference = EXCLUDED.last_payment_reference,
    updated_at = NOW()
  RETURNING id INTO subscription_id;
  
  -- Update user profile
  UPDATE public.user_profiles SET
    is_subscribed = true,
    subscription_expires_at = end_date,
    subscription_status = 'active',
    subscription_plan = p_plan_id,
    updated_at = NOW()
  WHERE user_id = user_record.id;
  
  RETURN jsonb_build_object(
    'success', true,
    'user_id', user_record.id,
    'payment_id', payment_id,
    'subscription_id', subscription_id,
    'expires_at', end_date
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 10: Final sync of all user emails
UPDATE public.user_profiles 
SET email = au.email, updated_at = now()
FROM auth.users au 
WHERE user_profiles.user_id = au.id 
AND (user_profiles.email IS NULL OR user_profiles.email != au.email);
